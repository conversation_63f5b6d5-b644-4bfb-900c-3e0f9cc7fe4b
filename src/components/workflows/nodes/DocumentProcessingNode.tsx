import { memo, useState } from 'react'
import { Handle, Position, NodeProps } from '@xyflow/react'

const DocumentProcessingNode = memo(({ data, selected }: NodeProps) => {
  const nodeData = data as any
  const [showTooltip, setShowTooltip] = useState(false)

  const getIcon = () => {
    if (nodeData.label?.toLowerCase().includes('upload')) {
      return (
        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
      )
    }
    if (nodeData.label?.toLowerCase().includes('review')) {
      return (
        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    }
    return (
      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
    )
  }

  return (
    <div
      className={`px-4 py-3 shadow-md rounded-lg bg-white border-2 ${
        selected ? 'border-blue-500' : 'border-gray-300'
      } min-w-[160px] relative`}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 !bg-gray-400 !border-2 !border-white"
      />

      <div className="flex items-center space-x-2">
        <div className="text-blue-600">
          {getIcon()}
        </div>
        <div className="flex-1">
          <div className="text-sm font-medium text-gray-900 truncate">
            {nodeData.label}
          </div>
          {nodeData.description && (
            <div className="text-xs text-gray-500 truncate">
              {nodeData.description}
            </div>
          )}
        </div>
      </div>

      {/* Status indicator */}
      {nodeData.config?.assignee && (
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full border-2 border-white" />
      )}

      {/* Tooltip */}
      {showTooltip && nodeData.description && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap z-10">
          {nodeData.description}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900" />
        </div>
      )}

      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 !bg-gray-400 !border-2 !border-white"
      />
    </div>
  )
})

DocumentProcessingNode.displayName = 'DocumentProcessingNode'

export default DocumentProcessingNode
