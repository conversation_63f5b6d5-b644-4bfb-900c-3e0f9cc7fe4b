import { useState } from 'react'
import { NodeType, Priority, NodeConfig } from '../../types'
import { useWorkflowDesigner } from '../../hooks/useWorkflows'
import { useNotifications } from '../../hooks/useUI'

interface NodeConfigModalProps {
  nodeId: string | null
  isOpen: boolean
  onClose: () => void
}

const NodeConfigModal = ({ nodeId, isOpen, onClose }: NodeConfigModalProps) => {
  const { nodes, updateNode } = useWorkflowDesigner()
  const { showSuccess, showError } = useNotifications()
  
  const node = nodes.find(n => n.id === nodeId)
  
  const [config, setConfig] = useState({
    label: node?.data?.label || '',
    description: node?.data?.description || '',
    assignee: node?.data?.config?.assignee || '',
    priority: node?.data?.config?.priority || Priority.MEDIUM,
    dueDate: node?.data?.config?.dueDate ? new Date(node.data.config.dueDate).toISOString().split('T')[0] : '',
    approvalRequired: node?.data?.config?.approvalRequired || false,
    autoApprove: node?.data?.config?.autoApprove || false,
    timeoutDuration: node?.data?.config?.timeoutDuration || 24,
    notificationEnabled: true,
    escalationEnabled: false,
    customSettings: node?.data?.config?.customSettings || {}
  })

  const handleSave = () => {
    if (!nodeId || !node) {
      showError('Error', 'Node not found')
      return
    }

    if (!config.label.trim()) {
      showError('Validation Error', 'Node label is required')
      return
    }

    const updatedNodeConfig: NodeConfig = {
      assignee: config.assignee || undefined,
      priority: config.priority,
      dueDate: config.dueDate ? new Date(config.dueDate) : undefined,
      approvalRequired: config.approvalRequired,
      autoApprove: config.autoApprove,
      timeoutDuration: config.timeoutDuration,
      customSettings: config.customSettings
    }

    updateNode(nodeId, {
      data: {
        ...node.data,
        label: config.label,
        description: config.description,
        config: updatedNodeConfig
      }
    })

    showSuccess('Node Updated', 'Node configuration has been saved')
    onClose()
  }

  const handleClose = () => {
    onClose()
  }

  if (!isOpen || !node) return null

  const getNodeTypeLabel = (type: NodeType) => {
    switch (type) {
      case NodeType.START: return 'Start Node'
      case NodeType.END: return 'End Node'
      case NodeType.DOCUMENT_UPLOAD: return 'Document Upload'
      case NodeType.DOCUMENT_REVIEW: return 'Document Review'
      case NodeType.APPROVAL: return 'Approval Node'
      case NodeType.CONDITIONAL: return 'Conditional Node'
      case NodeType.NOTIFICATION: return 'Notification Node'
      default: return 'Custom Node'
    }
  }

  const isConfigurable = node.type !== NodeType.START && node.type !== NodeType.END

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            Configure {getNodeTypeLabel(node.type as NodeType)}
          </h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="space-y-6">
          {/* Basic Information */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">Basic Information</h4>
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label htmlFor="label" className="block text-sm font-medium text-gray-700">
                  Label *
                </label>
                <input
                  type="text"
                  id="label"
                  value={config.label}
                  onChange={(e) => setConfig(prev => ({ ...prev, label: e.target.value }))}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter node label"
                />
              </div>
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <textarea
                  id="description"
                  rows={3}
                  value={config.description}
                  onChange={(e) => setConfig(prev => ({ ...prev, description: e.target.value }))}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter node description"
                />
              </div>
            </div>
          </div>

          {/* Assignment & Timing */}
          {isConfigurable && (
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">Assignment & Timing</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="assignee" className="block text-sm font-medium text-gray-700">
                    Assignee
                  </label>
                  <input
                    type="text"
                    id="assignee"
                    value={config.assignee}
                    onChange={(e) => setConfig(prev => ({ ...prev, assignee: e.target.value }))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter assignee email or ID"
                  />
                </div>
                <div>
                  <label htmlFor="priority" className="block text-sm font-medium text-gray-700">
                    Priority
                  </label>
                  <select
                    id="priority"
                    value={config.priority}
                    onChange={(e) => setConfig(prev => ({ ...prev, priority: e.target.value as Priority }))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value={Priority.LOW}>Low</option>
                    <option value={Priority.MEDIUM}>Medium</option>
                    <option value={Priority.HIGH}>High</option>
                    <option value={Priority.URGENT}>Urgent</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700">
                    Due Date
                  </label>
                  <input
                    type="date"
                    id="dueDate"
                    value={config.dueDate}
                    onChange={(e) => setConfig(prev => ({ ...prev, dueDate: e.target.value }))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label htmlFor="timeoutDuration" className="block text-sm font-medium text-gray-700">
                    Timeout (hours)
                  </label>
                  <input
                    type="number"
                    id="timeoutDuration"
                    min="1"
                    max="168"
                    value={config.timeoutDuration}
                    onChange={(e) => setConfig(prev => ({ ...prev, timeoutDuration: parseInt(e.target.value) || 24 }))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Approval Settings */}
          {(node.type === NodeType.APPROVAL || node.type === NodeType.DOCUMENT_REVIEW) && (
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">Approval Settings</h4>
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    id="approvalRequired"
                    type="checkbox"
                    checked={config.approvalRequired}
                    onChange={(e) => setConfig(prev => ({ ...prev, approvalRequired: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="approvalRequired" className="ml-2 block text-sm text-gray-900">
                    Approval Required
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="autoApprove"
                    type="checkbox"
                    checked={config.autoApprove}
                    onChange={(e) => setConfig(prev => ({ ...prev, autoApprove: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="autoApprove" className="ml-2 block text-sm text-gray-900">
                    Auto-approve on timeout
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* Notification Settings */}
          {node.type === NodeType.NOTIFICATION && (
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">Notification Settings</h4>
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    id="notificationEnabled"
                    type="checkbox"
                    checked={config.notificationEnabled}
                    onChange={(e) => setConfig(prev => ({ ...prev, notificationEnabled: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="notificationEnabled" className="ml-2 block text-sm text-gray-900">
                    Enable Notifications
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="escalationEnabled"
                    type="checkbox"
                    checked={config.escalationEnabled}
                    onChange={(e) => setConfig(prev => ({ ...prev, escalationEnabled: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="escalationEnabled" className="ml-2 block text-sm text-gray-900">
                    Enable Escalation
                  </label>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
          <button
            type="button"
            onClick={handleClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSave}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
          >
            Save Configuration
          </button>
        </div>
      </div>
    </div>
  )
}

export default NodeConfigModal
