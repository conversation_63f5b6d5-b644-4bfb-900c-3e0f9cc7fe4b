import { memo } from 'react'
import { Handle, Position, NodeProps } from '@xyflow/react'

const StartNode = memo(({ data, selected }: NodeProps) => {
  const nodeData = data as any
  return (
    <div className={`px-4 py-2 shadow-md rounded-full bg-green-500 border-2 ${
      selected ? 'border-green-700' : 'border-green-600'
    } min-w-[120px]`}>
      <div className="flex items-center justify-center">
        <div className="text-white font-medium text-sm text-center">
          <div className="flex items-center justify-center mb-1">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
            </svg>
            Start
          </div>
          {nodeData.label && nodeData.label !== 'Start' && (
            <div className="text-xs opacity-90">{nodeData.label}</div>
          )}
        </div>
      </div>

      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 !bg-green-600 !border-2 !border-white"
      />
    </div>
  )
})

StartNode.displayName = 'StartNode'

export default StartNode
