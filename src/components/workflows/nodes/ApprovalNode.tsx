import { memo, useState } from 'react'
import { Handle, Position, NodeProps } from '@xyflow/react'

const ApprovalNode = memo(({ data, selected }: NodeProps) => {
  const nodeData = data as any
  const [showTooltip, setShowTooltip] = useState(false)

  return (
    <div
      className={`px-4 py-3 shadow-md rounded-lg bg-amber-50 border-2 ${
        selected ? 'border-amber-500' : 'border-amber-300'
      } min-w-[160px] relative`}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 !bg-amber-500 !border-2 !border-white"
      />

      <div className="flex items-center space-x-2">
        <div className="text-amber-600">
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div className="flex-1">
          <div className="text-sm font-medium text-gray-900 truncate">
            {nodeData.label}
          </div>
          {nodeData.config?.assignee && (
            <div className="text-xs text-amber-700">
              Assigned to: {nodeData.config.assignee}
            </div>
          )}
          {nodeData.description && (
            <div className="text-xs text-gray-500 truncate">
              {nodeData.description}
            </div>
          )}
        </div>
      </div>

      {/* Priority indicator */}
      {nodeData.config?.priority && (
        <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
          nodeData.config.priority === 'high' ? 'bg-red-500' :
          nodeData.config.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
        }`} />
      )}

      {/* Tooltip */}
      {showTooltip && (nodeData.description || nodeData.config?.assignee) && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap z-10 max-w-xs">
          {nodeData.description && <div>{nodeData.description}</div>}
          {nodeData.config?.assignee && <div>Assignee: {nodeData.config.assignee}</div>}
          {nodeData.config?.priority && <div>Priority: {nodeData.config.priority}</div>}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900" />
        </div>
      )}

      {/* Multiple outputs for approval/rejection */}
      <Handle
        type="source"
        position={Position.Right}
        id="approved"
        className="w-3 h-3 !bg-green-500 !border-2 !border-white"
        style={{ top: '30%' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="rejected"
        className="w-3 h-3 !bg-red-500 !border-2 !border-white"
        style={{ top: '70%' }}
      />
    </div>
  )
})

ApprovalNode.displayName = 'ApprovalNode'

export default ApprovalNode
