import { useState } from 'react'
import { useWorkflowDesigner } from '../../hooks/useWorkflows'
import { useNotifications } from '../../hooks/useUI'
import WorkflowCanvas from './WorkflowCanvas'
import NodePalette from './NodePalette'
import WorkflowToolbar from './WorkflowToolbar'

interface WorkflowDesignerProps {
  workflowId?: string
  readOnly?: boolean
}

const WorkflowDesigner = ({ readOnly = false }: WorkflowDesignerProps) => {
  const {
    nodes,
    edges,
    mode,
    setMode,
    clear,
    hasNodes,
    isEditing
  } = useWorkflowDesigner()

  const { showSuccess, showError } = useNotifications()
  const [showPalette, setShowPalette] = useState(!readOnly)
  const [workflowName, setWorkflowName] = useState('Untitled Workflow')
  const [workflowDescription, setWorkflowDescription] = useState('')

  const handleSave = async () => {
    if (!hasNodes) {
      showError('Cannot Save', 'Please add at least one node to the workflow')
      return
    }

    // Check if workflow has start and end nodes
    const hasStart = nodes.some(node => node.type === 'start')
    const hasEnd = nodes.some(node => node.type === 'end')

    if (!hasStart) {
      showError('Invalid Workflow', 'Workflow must have a Start node')
      return
    }

    if (!hasEnd) {
      showError('Invalid Workflow', 'Workflow must have an End node')
      return
    }

    try {
      // Here you would save the workflow to your backend
      // For now, we'll just show a success message
      showSuccess('Workflow Saved', `"${workflowName}" has been saved successfully`)
    } catch (error) {
      showError('Save Failed', 'Failed to save workflow')
    }
  }

  const handleClear = () => {
    if (window.confirm('Are you sure you want to clear the entire workflow? This action cannot be undone.')) {
      clear()
      showSuccess('Workflow Cleared', 'Workflow has been cleared')
    }
  }

  const handleModeChange = (newMode: 'view' | 'edit' | 'create') => {
    setMode(newMode)
    if (newMode === 'view') {
      setShowPalette(false)
    } else {
      setShowPalette(true)
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Toolbar */}
      <WorkflowToolbar
        workflowName={workflowName}
        workflowDescription={workflowDescription}
        onNameChange={setWorkflowName}
        onDescriptionChange={setWorkflowDescription}
        mode={mode}
        onModeChange={handleModeChange}
        onSave={handleSave}
        onClear={handleClear}
        showPalette={showPalette}
        onTogglePalette={() => setShowPalette(!showPalette)}
        readOnly={readOnly}
        hasNodes={hasNodes}
      />

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Node Palette */}
        <NodePalette isVisible={showPalette && isEditing} />

        {/* Workflow Canvas */}
        <div className="flex-1 relative">
          <WorkflowCanvas readOnly={readOnly} />
        </div>
      </div>

      {/* Status Bar */}
      <div className="bg-gray-50 border-t border-gray-200 px-4 py-2 flex items-center justify-between text-sm text-gray-600">
        <div className="flex items-center space-x-4">
          <span>{nodes.length} nodes</span>
          <span>{edges.length} connections</span>
          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
            mode === 'create' ? 'bg-blue-100 text-blue-800' :
            mode === 'edit' ? 'bg-yellow-100 text-yellow-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {mode === 'create' ? 'Creating' : mode === 'edit' ? 'Editing' : 'Viewing'}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          {isEditing && (
            <span className="text-xs text-gray-500">
              Drag nodes from sidebar • Connect with handles • Delete with backspace
            </span>
          )}
        </div>
      </div>
    </div>
  )
}

export default WorkflowDesigner
