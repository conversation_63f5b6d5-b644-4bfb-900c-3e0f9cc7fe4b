import { useState } from 'react'
import { Document, ConfidentialityLevel } from '../../types'
import { useDocumentStore } from '../../store/documentStore'
import { useNotifications } from '../../hooks/useUI'

interface DocumentMetadataEditorProps {
  document: Document
  isOpen: boolean
  onClose: () => void
}

const DocumentMetadataEditor = ({ document, isOpen, onClose }: DocumentMetadataEditorProps) => {
  const { updateDocument } = useDocumentStore()
  const { showSuccess, showError } = useNotifications()
  const [loading, setLoading] = useState(false)

  const [metadata, setMetadata] = useState({
    title: document.metadata.title || document.name,
    description: document.metadata.description || '',
    category: document.metadata.category || '',
    department: document.metadata.department || '',
    confidentiality: document.metadata.confidentiality || ConfidentialityLevel.INTERNAL,
    tags: document.tags.join(', ')
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const updatedMetadata = {
        ...document.metadata,
        title: metadata.title,
        description: metadata.description,
        category: metadata.category,
        department: metadata.department,
        confidentiality: metadata.confidentiality
      }

      const updatedTags = metadata.tags.split(',').map(tag => tag.trim()).filter(Boolean)

      updateDocument(document.id, {
        metadata: updatedMetadata,
        tags: updatedTags
      })

      showSuccess('Metadata Updated', 'Document metadata has been successfully updated')
      onClose()
    } catch (error) {
      showError('Update Failed', 'Failed to update document metadata')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!loading) {
      onClose()
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Edit Document Metadata</h3>
          <button
            onClick={handleClose}
            disabled={loading}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div className="sm:col-span-2">
              <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                Title *
              </label>
              <input
                type="text"
                id="title"
                required
                value={metadata.title}
                onChange={(e) => setMetadata(prev => ({ ...prev, title: e.target.value }))}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                disabled={loading}
              />
            </div>

            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                Category
              </label>
              <select
                id="category"
                value={metadata.category}
                onChange={(e) => setMetadata(prev => ({ ...prev, category: e.target.value }))}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                disabled={loading}
              >
                <option value="">Select category</option>
                <option value="HR Documents">HR Documents</option>
                <option value="Financial">Financial</option>
                <option value="Project Management">Project Management</option>
                <option value="Legal">Legal</option>
                <option value="Technical">Technical</option>
                <option value="Marketing">Marketing</option>
                <option value="General">General</option>
              </select>
            </div>

            <div>
              <label htmlFor="department" className="block text-sm font-medium text-gray-700">
                Department
              </label>
              <input
                type="text"
                id="department"
                value={metadata.department}
                onChange={(e) => setMetadata(prev => ({ ...prev, department: e.target.value }))}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                disabled={loading}
              />
            </div>

            <div className="sm:col-span-2">
              <label htmlFor="confidentiality" className="block text-sm font-medium text-gray-700">
                Confidentiality Level
              </label>
              <select
                id="confidentiality"
                value={metadata.confidentiality}
                onChange={(e) => setMetadata(prev => ({ ...prev, confidentiality: e.target.value as ConfidentialityLevel }))}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                disabled={loading}
              >
                <option value={ConfidentialityLevel.PUBLIC}>Public</option>
                <option value={ConfidentialityLevel.INTERNAL}>Internal</option>
                <option value={ConfidentialityLevel.CONFIDENTIAL}>Confidential</option>
                <option value={ConfidentialityLevel.RESTRICTED}>Restricted</option>
              </select>
            </div>

            <div className="sm:col-span-2">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                id="description"
                rows={3}
                value={metadata.description}
                onChange={(e) => setMetadata(prev => ({ ...prev, description: e.target.value }))}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                disabled={loading}
              />
            </div>

            <div className="sm:col-span-2">
              <label htmlFor="tags" className="block text-sm font-medium text-gray-700">
                Tags (comma-separated)
              </label>
              <input
                type="text"
                id="tags"
                placeholder="e.g. handbook, hr, policies"
                value={metadata.tags}
                onChange={(e) => setMetadata(prev => ({ ...prev, tags: e.target.value }))}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                disabled={loading}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {loading && (
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              )}
              {loading ? 'Updating...' : 'Update Metadata'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default DocumentMetadataEditor
