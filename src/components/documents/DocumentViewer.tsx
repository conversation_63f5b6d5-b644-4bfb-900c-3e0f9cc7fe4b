import { useState, useEffect } from 'react'
import { Document, DocumentType } from '../../types'
import { formatFileSize, formatDate, getDocumentStatusColor } from '../../utils/dataUtils'

interface DocumentViewerProps {
  document: Document | null
  isOpen: boolean
  onClose: () => void
}

const DocumentViewer = ({ document, isOpen, onClose }: DocumentViewerProps) => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (document && isOpen) {
      setLoading(true)
      setError(null)

      // Simulate loading document content
      setTimeout(() => {
        setLoading(false)
      }, 1000)
    }
  }, [document, isOpen])

  if (!isOpen || !document) return null

  const renderDocumentContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading document...</p>
          </div>
        </div>
      )
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <svg className="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <p className="mt-4 text-red-600">{error}</p>
          </div>
        </div>
      )
    }

    switch (document.type) {
      case DocumentType.PDF:
        return <PDFViewer document={document} />
      case DocumentType.IMAGE:
        return <ImageViewer document={document} />
      case DocumentType.TEXT:
        return <TextViewer document={document} />
      default:
        return <DefaultViewer document={document} />
    }
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-5 border w-full max-w-6xl shadow-lg rounded-md bg-white min-h-[90vh]">
        {/* Header */}
        <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
          <div className="flex-1 min-w-0">
            <h2 className="text-xl font-semibold text-gray-900 truncate">
              {document.name}
            </h2>
            <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
              <span>{formatFileSize(document.size)}</span>
              <span>•</span>
              <span>Updated {formatDate(document.updatedAt)}</span>
              <span>•</span>
              <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getDocumentStatusColor(document.status)}`}>
                {document.status.replace('_', ' ')}
              </span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => window.open(document.url || '#', '_blank')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Download
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Document Content */}
        <div className="flex-1">
          {renderDocumentContent()}
        </div>

        {/* Document Metadata */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Document Information</h4>
              <dl className="space-y-1 text-sm">
                <div>
                  <dt className="text-gray-500 inline">Type:</dt>
                  <dd className="text-gray-900 inline ml-2">{document.type.toUpperCase()}</dd>
                </div>
                <div>
                  <dt className="text-gray-500 inline">Version:</dt>
                  <dd className="text-gray-900 inline ml-2">{document.version}</dd>
                </div>
                <div>
                  <dt className="text-gray-500 inline">Created:</dt>
                  <dd className="text-gray-900 inline ml-2">{formatDate(document.createdAt)}</dd>
                </div>
              </dl>
            </div>

            {document.metadata && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Metadata</h4>
                <dl className="space-y-1 text-sm">
                  {document.metadata.category && (
                    <div>
                      <dt className="text-gray-500 inline">Category:</dt>
                      <dd className="text-gray-900 inline ml-2">{document.metadata.category}</dd>
                    </div>
                  )}
                  {document.metadata.department && (
                    <div>
                      <dt className="text-gray-500 inline">Department:</dt>
                      <dd className="text-gray-900 inline ml-2">{document.metadata.department}</dd>
                    </div>
                  )}
                  {document.metadata.confidentiality && (
                    <div>
                      <dt className="text-gray-500 inline">Confidentiality:</dt>
                      <dd className="text-gray-900 inline ml-2">{document.metadata.confidentiality}</dd>
                    </div>
                  )}
                </dl>
              </div>
            )}

            {document.tags.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Tags</h4>
                <div className="flex flex-wrap gap-1">
                  {document.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          {document.metadata.description && (
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Description</h4>
              <p className="text-sm text-gray-700">{document.metadata.description}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

const PDFViewer = ({ }: { document: Document }) => (
  <div className="bg-gray-100 rounded-lg p-8 text-center h-96 flex items-center justify-center">
    <div>
      <svg className="mx-auto h-16 w-16 text-red-400 mb-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
      </svg>
      <h3 className="text-lg font-medium text-gray-900 mb-2">PDF Document</h3>
      <p className="text-gray-600 mb-4">PDF viewer would be integrated here</p>
      <p className="text-sm text-gray-500">
        In a production app, you would integrate a PDF viewer library like react-pdf or pdf.js
      </p>
    </div>
  </div>
)

const ImageViewer = ({ document }: { document: Document }) => (
  <div className="bg-gray-100 rounded-lg p-8 text-center h-96 flex items-center justify-center">
    <div>
      <svg className="mx-auto h-16 w-16 text-green-400 mb-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
      </svg>
      <h3 className="text-lg font-medium text-gray-900 mb-2">Image File</h3>
      <p className="text-gray-600 mb-4">Image preview would be shown here</p>
      <p className="text-sm text-gray-500">
        Actual image content: {document.name}
      </p>
    </div>
  </div>
)

const TextViewer = ({ document }: { document: Document }) => (
  <div className="bg-white border rounded-lg p-6 h-96 overflow-y-auto">
    <div className="font-mono text-sm text-gray-800 whitespace-pre-wrap">
      {document.content || `This is a sample text document: ${document.name}

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.`}
    </div>
  </div>
)

const DefaultViewer = ({ document }: { document: Document }) => (
  <div className="bg-gray-50 rounded-lg p-8 text-center h-96 flex items-center justify-center">
    <div>
      <svg className="mx-auto h-16 w-16 text-gray-400 mb-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clipRule="evenodd" />
      </svg>
      <h3 className="text-lg font-medium text-gray-900 mb-2">Document Preview</h3>
      <p className="text-gray-600 mb-4">Preview not available for this file type</p>
      <p className="text-sm text-gray-500">
        File: {document.name} ({document.type.toUpperCase()})
      </p>
      <button
        onClick={() => window.open(document.url || '#', '_blank')}
        className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
      >
        Download to View
      </button>
    </div>
  </div>
)

export default DocumentViewer
