import { useState } from 'react'
import { useWorkflows } from '../../hooks/useWorkflows'
import { useNotifications } from '../../hooks/useUI'
import { WorkflowInstance, WorkflowStatus } from '../../types'
import { formatRelativeTime, getWorkflowStatusColor } from '../../utils/dataUtils'
import WorkflowExecutor from './WorkflowExecutor'
import WorkflowDesigner from './WorkflowDesigner'

const WorkflowList = () => {
  const {
    instances,
    loading,
    error,
    hasInstances,
    startInstance,
    pauseInstance,
    resumeInstance,
    cancelInstance
  } = useWorkflows()

  const { showSuccess, showError } = useNotifications()

  const [selectedWorkflow, setSelectedWorkflow] = useState<WorkflowInstance | null>(null)
  const [showExecutor, setShowExecutor] = useState(false)
  const [showDesigner, setShowDesigner] = useState(false)
  const [filterStatus, setFilterStatus] = useState<WorkflowStatus | 'all'>('all')

  const filteredInstances = instances.filter(instance =>
    filterStatus === 'all' || instance.status === filterStatus
  )

  const handleCreateWorkflow = () => {
    setShowDesigner(true)
  }

  const handleExecuteWorkflow = (workflow: WorkflowInstance) => {
    setSelectedWorkflow(workflow)
    setShowExecutor(true)
  }

  const handleStartWorkflow = async (id: string) => {
    try {
      await startInstance(id)
      showSuccess('Workflow Started', 'Workflow has been started successfully')
    } catch (error) {
      showError('Start Failed', 'Failed to start workflow')
    }
  }

  const handlePauseWorkflow = async (id: string) => {
    try {
      await pauseInstance(id)
      showSuccess('Workflow Paused', 'Workflow has been paused')
    } catch (error) {
      showError('Pause Failed', 'Failed to pause workflow')
    }
  }

  const handleResumeWorkflow = async (id: string) => {
    try {
      await resumeInstance(id)
      showSuccess('Workflow Resumed', 'Workflow has been resumed')
    } catch (error) {
      showError('Resume Failed', 'Failed to resume workflow')
    }
  }

  const handleCancelWorkflow = async (id: string) => {
    if (window.confirm('Are you sure you want to cancel this workflow?')) {
      try {
        await cancelInstance(id)
        showSuccess('Workflow Cancelled', 'Workflow has been cancelled')
      } catch (error) {
        showError('Cancel Failed', 'Failed to cancel workflow')
      }
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Workflows</h2>
          <p className="mt-1 text-sm text-gray-600">
            Manage and execute your document workflows
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={handleCreateWorkflow}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
          >
            <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Create Workflow
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="flex items-center space-x-4">
          <label htmlFor="status-filter" className="text-sm font-medium text-gray-700">
            Filter by status:
          </label>
          <select
            id="status-filter"
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as WorkflowStatus | 'all')}
            className="border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Statuses</option>
            <option value={WorkflowStatus.DRAFT}>Draft</option>
            <option value={WorkflowStatus.ACTIVE}>Active</option>
            <option value={WorkflowStatus.PAUSED}>Paused</option>
            <option value={WorkflowStatus.COMPLETED}>Completed</option>
            <option value={WorkflowStatus.CANCELLED}>Cancelled</option>
            <option value={WorkflowStatus.FAILED}>Failed</option>
          </select>
          <div className="text-sm text-gray-500">
            {filteredInstances.length} workflow{filteredInstances.length !== 1 ? 's' : ''}
          </div>
        </div>
      </div>

      {/* Workflow List */}
      {!hasInstances ? (
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No workflows</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating your first workflow.</p>
          <div className="mt-6">
            <button
              onClick={handleCreateWorkflow}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Create Workflow
            </button>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {filteredInstances.map((workflow) => (
              <li key={workflow.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-blue-600 truncate">
                          {workflow.name}
                        </p>
                        <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getWorkflowStatusColor(workflow.status)}`}>
                          {workflow.status}
                        </span>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500">
                        <p className="truncate">
                          {workflow.description || 'No description'}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 space-x-4">
                        <span>Progress: {workflow.progress}%</span>
                        <span>Created {formatRelativeTime(workflow.createdAt)}</span>
                        <span>{workflow.documents.length} document{workflow.documents.length !== 1 ? 's' : ''}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {workflow.status === WorkflowStatus.DRAFT && (
                        <button
                          onClick={() => handleStartWorkflow(workflow.id)}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                        >
                          Start
                        </button>
                      )}
                      {workflow.status === WorkflowStatus.ACTIVE && (
                        <>
                          <button
                            onClick={() => handleExecuteWorkflow(workflow)}
                            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                          >
                            Execute
                          </button>
                          <button
                            onClick={() => handlePauseWorkflow(workflow.id)}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                          >
                            Pause
                          </button>
                        </>
                      )}
                      {workflow.status === WorkflowStatus.PAUSED && (
                        <button
                          onClick={() => handleResumeWorkflow(workflow.id)}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                        >
                          Resume
                        </button>
                      )}
                      {(workflow.status === WorkflowStatus.ACTIVE || workflow.status === WorkflowStatus.PAUSED) && (
                        <button
                          onClick={() => handleCancelWorkflow(workflow.id)}
                          className="inline-flex items-center px-3 py-2 border border-red-300 text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
                        >
                          Cancel
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Workflow Executor Modal */}
      {showExecutor && selectedWorkflow && (
        <WorkflowExecutor
          workflowInstance={selectedWorkflow}
          onClose={() => {
            setShowExecutor(false)
            setSelectedWorkflow(null)
          }}
        />
      )}

      {/* Workflow Designer Modal */}
      {showDesigner && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-4 mx-auto border w-full max-w-7xl shadow-lg rounded-md bg-white min-h-[90vh]">
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-medium text-gray-900">Create New Workflow</h3>
              <button
                onClick={() => setShowDesigner(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="h-full">
              <WorkflowDesigner />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default WorkflowList
