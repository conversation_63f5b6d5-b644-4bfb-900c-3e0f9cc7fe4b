import { useState } from 'react'
import { useDocumentSearch } from '../../hooks/useDocuments'
import { DocumentType, DocumentStatus } from '../../types'

const DocumentSearch = () => {
  const { searchFilters, updateFilters, clearFilters, hasActiveFilters } = useDocumentSearch()
  const [showAdvanced, setShowAdvanced] = useState(false)

  const handleQueryChange = (query: string) => {
    updateFilters({ query: query || undefined })
  }

  const handleTypeChange = (type: string) => {
    updateFilters({ type: type ? (type as DocumentType) : undefined })
  }

  const handleStatusChange = (status: string) => {
    updateFilters({ status: status ? (status as DocumentStatus) : undefined })
  }

  const handleTagsChange = (tags: string) => {
    const tagArray = tags.split(',').map(tag => tag.trim()).filter(Boolean)
    updateFilters({ tags: tagArray.length > 0 ? tagArray : undefined })
  }

  const handleDateFromChange = (date: string) => {
    updateFilters({ dateFrom: date ? new Date(date) : undefined })
  }

  const handleDateToChange = (date: string) => {
    updateFilters({ dateTo: date ? new Date(date) : undefined })
  }

  return (
    <div className="bg-white shadow rounded-lg p-4">
      <div className="space-y-4">
        {/* Basic Search */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <label htmlFor="search" className="sr-only">
              Search documents
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                id="search"
                name="search"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Search documents..."
                type="search"
                value={searchFilters.query || ''}
                onChange={(e) => handleQueryChange(e.target.value)}
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className={`px-4 py-2 text-sm font-medium rounded-md border ${
                showAdvanced
                  ? 'bg-blue-50 border-blue-500 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              Advanced
            </button>
            
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Clear
              </button>
            )}
          </div>
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
            {/* Document Type */}
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                Type
              </label>
              <select
                id="type"
                value={searchFilters.type || ''}
                onChange={(e) => handleTypeChange(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Types</option>
                <option value={DocumentType.PDF}>PDF</option>
                <option value={DocumentType.WORD}>Word</option>
                <option value={DocumentType.EXCEL}>Excel</option>
                <option value={DocumentType.POWERPOINT}>PowerPoint</option>
                <option value={DocumentType.IMAGE}>Image</option>
                <option value={DocumentType.TEXT}>Text</option>
                <option value={DocumentType.OTHER}>Other</option>
              </select>
            </div>

            {/* Status */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                value={searchFilters.status || ''}
                onChange={(e) => handleStatusChange(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Statuses</option>
                <option value={DocumentStatus.DRAFT}>Draft</option>
                <option value={DocumentStatus.UNDER_REVIEW}>Under Review</option>
                <option value={DocumentStatus.APPROVED}>Approved</option>
                <option value={DocumentStatus.REJECTED}>Rejected</option>
                <option value={DocumentStatus.ARCHIVED}>Archived</option>
              </select>
            </div>

            {/* Date From */}
            <div>
              <label htmlFor="dateFrom" className="block text-sm font-medium text-gray-700 mb-1">
                From Date
              </label>
              <input
                type="date"
                id="dateFrom"
                value={searchFilters.dateFrom ? searchFilters.dateFrom.toISOString().split('T')[0] : ''}
                onChange={(e) => handleDateFromChange(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Date To */}
            <div>
              <label htmlFor="dateTo" className="block text-sm font-medium text-gray-700 mb-1">
                To Date
              </label>
              <input
                type="date"
                id="dateTo"
                value={searchFilters.dateTo ? searchFilters.dateTo.toISOString().split('T')[0] : ''}
                onChange={(e) => handleDateToChange(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Tags */}
            <div className="sm:col-span-2">
              <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-1">
                Tags (comma-separated)
              </label>
              <input
                type="text"
                id="tags"
                placeholder="e.g. handbook, hr, policies"
                value={searchFilters.tags?.join(', ') || ''}
                onChange={(e) => handleTagsChange(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        )}

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 pt-2 border-t border-gray-200">
            <span className="text-sm text-gray-500">Active filters:</span>
            {searchFilters.query && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Query: "{searchFilters.query}"
                <button
                  onClick={() => updateFilters({ query: undefined })}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            )}
            {searchFilters.type && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Type: {searchFilters.type}
                <button
                  onClick={() => updateFilters({ type: undefined })}
                  className="ml-1 text-green-600 hover:text-green-800"
                >
                  ×
                </button>
              </span>
            )}
            {searchFilters.status && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Status: {searchFilters.status.replace('_', ' ')}
                <button
                  onClick={() => updateFilters({ status: undefined })}
                  className="ml-1 text-yellow-600 hover:text-yellow-800"
                >
                  ×
                </button>
              </span>
            )}
            {searchFilters.tags && searchFilters.tags.length > 0 && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Tags: {searchFilters.tags.join(', ')}
                <button
                  onClick={() => updateFilters({ tags: undefined })}
                  className="ml-1 text-purple-600 hover:text-purple-800"
                >
                  ×
                </button>
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default DocumentSearch
