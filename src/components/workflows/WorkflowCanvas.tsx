import { useCallback, useMemo } from 'react'
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,

  Connection,
  Edge,
  Node,
  NodeTypes,
  EdgeTypes,
  Panel,
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'

import { useWorkflowDesigner } from '../../hooks/useWorkflows'
import { NodeType } from '../../types'

// Import custom node components
import StartNode from './nodes/StartNode'
import EndNode from './nodes/EndNode'
import DocumentProcessingNode from './nodes/DocumentProcessingNode'
import ApprovalNode from './nodes/ApprovalNode'
import ConditionalNode from './nodes/ConditionalNode'
import NotificationNode from './nodes/NotificationNode'

// Import custom edge components
import DefaultEdge from './edges/DefaultEdge'
import ConditionalEdge from './edges/ConditionalEdge'

interface WorkflowCanvasProps {
  readOnly?: boolean
  className?: string
}

const WorkflowCanvas = ({ readOnly = false, className = '' }: WorkflowCanvasProps) => {
  const {
    nodes: designerNodes,
    edges: designerEdges,
    mode,
    setNodes,
    setEdges,
    addNode,
    addEdge: addDesignerEdge,
    deleteNode,
    deleteEdge,
    isEditing
  } = useWorkflowDesigner()

  const [nodes, , onNodesChange] = useNodesState(designerNodes as unknown as Node[])
  const [edges, , onEdgesChange] = useEdgesState(designerEdges as unknown as Edge[])

  // Update local state when designer state changes
  useMemo(() => {
    setNodes(designerNodes as unknown as Node[])
  }, [designerNodes, setNodes])

  useMemo(() => {
    setEdges(designerEdges as unknown as Edge[])
  }, [designerEdges, setEdges])

  // Define custom node types
  const nodeTypes: NodeTypes = useMemo(
    () => ({
      [NodeType.START]: StartNode,
      [NodeType.END]: EndNode,
      [NodeType.DOCUMENT_UPLOAD]: DocumentProcessingNode,
      [NodeType.DOCUMENT_REVIEW]: DocumentProcessingNode,
      [NodeType.APPROVAL]: ApprovalNode,
      [NodeType.CONDITIONAL]: ConditionalNode,
      [NodeType.NOTIFICATION]: NotificationNode,
      [NodeType.CUSTOM]: DocumentProcessingNode,
    }),
    []
  )

  // Define custom edge types
  const edgeTypes: EdgeTypes = useMemo(
    () => ({
      default: DefaultEdge,
      conditional: ConditionalEdge,
    }),
    []
  )

  // Handle connection creation
  const onConnect = useCallback(
    (params: Connection) => {
      if (readOnly || !isEditing) return

      const newEdge: Edge = {
        ...params,
        id: `edge-${Date.now()}`,
        type: 'default',
      }

      addDesignerEdge({
        id: newEdge.id,
        source: newEdge.source!,
        target: newEdge.target!,
        type: 'default',
      })
    },
    [readOnly, isEditing, addDesignerEdge]
  )

  // Handle node deletion
  const onNodesDelete = useCallback(
    (nodesToDelete: Node[]) => {
      if (readOnly || !isEditing) return

      nodesToDelete.forEach((node) => {
        deleteNode(node.id)
      })
    },
    [readOnly, isEditing, deleteNode]
  )

  // Handle edge deletion
  const onEdgesDelete = useCallback(
    (edgesToDelete: Edge[]) => {
      if (readOnly || !isEditing) return

      edgesToDelete.forEach((edge) => {
        deleteEdge(edge.id)
      })
    },
    [readOnly, isEditing, deleteEdge]
  )

  // Handle drag over for node dropping
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'move'
  }, [])

  // Handle node drop
  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault()

      if (readOnly || !isEditing) return

      const type = event.dataTransfer.getData('application/reactflow')
      if (!type) return

      const reactFlowBounds = (event.target as Element).getBoundingClientRect()
      const position = {
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      }

      const newNode = {
        id: `${type}-${Date.now()}`,
        type: type as NodeType,
        position,
        data: {
          label: getDefaultNodeLabel(type as NodeType),
          description: '',
        },
      }

      addNode(newNode)
    },
    [readOnly, isEditing, addNode]
  )

  const getDefaultNodeLabel = (type: NodeType): string => {
    switch (type) {
      case NodeType.START:
        return 'Start'
      case NodeType.END:
        return 'End'
      case NodeType.DOCUMENT_UPLOAD:
        return 'Document Upload'
      case NodeType.DOCUMENT_REVIEW:
        return 'Document Review'
      case NodeType.APPROVAL:
        return 'Approval'
      case NodeType.CONDITIONAL:
        return 'Conditional'
      case NodeType.NOTIFICATION:
        return 'Notification'
      default:
        return 'Custom Node'
    }
  }

  return (
    <div className={`h-full w-full ${className}`}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodesDelete={onNodesDelete}
        onEdgesDelete={onEdgesDelete}
        onDrop={onDrop}
        onDragOver={onDragOver}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        deleteKeyCode={readOnly ? null : ['Backspace', 'Delete']}
        multiSelectionKeyCode={readOnly ? null : ['Meta', 'Ctrl']}
        panOnDrag={!readOnly}
        nodesDraggable={!readOnly && isEditing}
        nodesConnectable={!readOnly && isEditing}
        elementsSelectable={!readOnly}
      >
        <Background color="#f1f5f9" gap={20} />
        <Controls showInteractive={!readOnly} />
        <MiniMap
          nodeColor={(node) => {
            switch (node.type) {
              case NodeType.START:
                return '#10b981'
              case NodeType.END:
                return '#ef4444'
              case NodeType.APPROVAL:
                return '#f59e0b'
              case NodeType.CONDITIONAL:
                return '#8b5cf6'
              default:
                return '#6b7280'
            }
          }}
          maskColor="rgba(0, 0, 0, 0.1)"
        />

        {/* Workflow Info Panel */}
        <Panel position="top-left" className="bg-white rounded-lg shadow-md p-4 m-4">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              mode === 'create' ? 'bg-blue-500' :
              mode === 'edit' ? 'bg-yellow-500' : 'bg-gray-500'
            }`} />
            <span className="text-sm font-medium text-gray-700">
              {mode === 'create' ? 'Creating Workflow' :
               mode === 'edit' ? 'Editing Workflow' : 'Viewing Workflow'}
            </span>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            {nodes.length} nodes, {edges.length} connections
          </div>
        </Panel>

        {/* Instructions Panel */}
        {isEditing && (
          <Panel position="top-right" className="bg-blue-50 rounded-lg shadow-md p-4 m-4 max-w-xs">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Instructions</h4>
            <ul className="text-xs text-blue-700 space-y-1">
              <li>• Drag nodes from the sidebar to add them</li>
              <li>• Connect nodes by dragging from handles</li>
              <li>• Select and press Delete to remove items</li>
              <li>• Double-click nodes to configure them</li>
            </ul>
          </Panel>
        )}
      </ReactFlow>
    </div>
  )
}

export default WorkflowCanvas
