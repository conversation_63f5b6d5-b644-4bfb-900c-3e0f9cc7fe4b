{"name": "hoso", "version": "1.0.0", "description": "", "main": "dist/index.js", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@xyflow/react": "^12.6.4", "date-fns": "^4.1.0", "lodash": "^4.17.21", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.1", "uuid": "^11.1.0", "zustand": "^5.0.5"}, "devDependencies": {"@types/lodash": "^4.17.17", "@types/node": "^22.15.29", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "@vitejs/plugin-react": "^4.5.0", "eslint": "^9.28.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "prettier": "^3.5.3", "typescript": "^5.5.3", "vite": "^6.3.5"}, "private": true}