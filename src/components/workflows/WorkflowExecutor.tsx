import { useState, useEffect } from 'react'
import { WorkflowInstance, WorkflowStatus } from '../../types'
import { useWorkflows } from '../../hooks/useWorkflows'
import { useNotifications } from '../../hooks/useUI'
import WorkflowCanvas from './WorkflowCanvas'

interface WorkflowExecutorProps {
  workflowInstance: WorkflowInstance
  onClose: () => void
}

const WorkflowExecutor = ({ workflowInstance, onClose }: WorkflowExecutorProps) => {
  const { updateInstance } = useWorkflows()
  const { showSuccess, showError, showInfo } = useNotifications()

  const [currentStep, setCurrentStep] = useState(workflowInstance.currentStep || '')
  const [executionLog, setExecutionLog] = useState<string[]>([])
  const [isExecuting, setIsExecuting] = useState(false)

  useEffect(() => {
    if (workflowInstance.status === WorkflowStatus.ACTIVE) {
      addToLog(`Workflow "${workflowInstance.name}" is running`)
    }
  }, [workflowInstance])

  const addToLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setExecutionLog(prev => [...prev, `[${timestamp}] ${message}`])
  }

  const executeStep = async (stepId: string) => {
    setIsExecuting(true)
    addToLog(`Executing step: ${stepId}`)

    try {
      // Simulate step execution
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Update step status
      setCurrentStep(stepId)
      addToLog(`Step ${stepId} completed successfully`)

      // Check if workflow is complete
      const isLastStep = stepId.includes('end')
      if (isLastStep) {
        updateInstance(workflowInstance.id, {
          status: WorkflowStatus.COMPLETED,
          completedAt: new Date(),
          progress: 100
        })
        addToLog('Workflow completed successfully')
        showSuccess('Workflow Complete', 'The workflow has been completed successfully')
      } else {
        // Update progress
        const progress = Math.min(workflowInstance.progress + 25, 90)
        updateInstance(workflowInstance.id, {
          currentStep: stepId,
          progress
        })
      }
    } catch (error) {
      addToLog(`Step ${stepId} failed: ${error}`)
      showError('Execution Error', `Failed to execute step: ${stepId}`)
    } finally {
      setIsExecuting(false)
    }
  }

  const pauseWorkflow = async () => {
    updateInstance(workflowInstance.id, {
      status: WorkflowStatus.PAUSED
    })
    addToLog('Workflow paused')
    showInfo('Workflow Paused', 'The workflow has been paused')
  }

  const resumeWorkflow = async () => {
    updateInstance(workflowInstance.id, {
      status: WorkflowStatus.ACTIVE
    })
    addToLog('Workflow resumed')
    showInfo('Workflow Resumed', 'The workflow has been resumed')
  }

  const cancelWorkflow = async () => {
    if (window.confirm('Are you sure you want to cancel this workflow?')) {
      updateInstance(workflowInstance.id, {
        status: WorkflowStatus.CANCELLED
      })
      addToLog('Workflow cancelled')
      showInfo('Workflow Cancelled', 'The workflow has been cancelled')
      onClose()
    }
  }

  const getStatusColor = (status: WorkflowStatus) => {
    switch (status) {
      case WorkflowStatus.ACTIVE:
        return 'bg-green-100 text-green-800'
      case WorkflowStatus.PAUSED:
        return 'bg-yellow-100 text-yellow-800'
      case WorkflowStatus.COMPLETED:
        return 'bg-blue-100 text-blue-800'
      case WorkflowStatus.CANCELLED:
        return 'bg-red-100 text-red-800'
      case WorkflowStatus.FAILED:
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-5 border w-full max-w-7xl shadow-lg rounded-md bg-white min-h-[90vh]">
        {/* Header */}
        <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
          <div className="flex-1 min-w-0">
            <h2 className="text-xl font-semibold text-gray-900 truncate">
              Workflow Execution: {workflowInstance.name}
            </h2>
            <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(workflowInstance.status)}`}>
                {workflowInstance.status}
              </span>
              <span>Progress: {workflowInstance.progress}%</span>
              <span>Current Step: {currentStep || 'Not started'}</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {workflowInstance.status === WorkflowStatus.ACTIVE && (
              <>
                <button
                  onClick={pauseWorkflow}
                  disabled={isExecuting}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zM12 8a1 1 0 012 0v4a1 1 0 11-2 0V8z" clipRule="evenodd" />
                  </svg>
                  Pause
                </button>
              </>
            )}
            {workflowInstance.status === WorkflowStatus.PAUSED && (
              <button
                onClick={resumeWorkflow}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
                Resume
              </button>
            )}
            <button
              onClick={cancelWorkflow}
              disabled={isExecuting}
              className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex h-full">
          {/* Workflow Canvas */}
          <div className="flex-1 mr-6">
            <div className="bg-gray-50 rounded-lg h-96">
              <WorkflowCanvas readOnly={true} />
            </div>
          </div>

          {/* Execution Panel */}
          <div className="w-80 space-y-4">
            {/* Progress */}
            <div className="bg-white border rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Progress</h3>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${workflowInstance.progress}%` }}
                />
              </div>
              <div className="mt-2 text-sm text-gray-600">
                {workflowInstance.progress}% Complete
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white border rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Quick Actions</h3>
              <div className="space-y-2">
                <button
                  onClick={() => executeStep('step-1')}
                  disabled={isExecuting || workflowInstance.status !== WorkflowStatus.ACTIVE}
                  className="w-full px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isExecuting ? 'Executing...' : 'Execute Next Step'}
                </button>
                <button
                  onClick={() => executeStep('approval-step')}
                  disabled={isExecuting || workflowInstance.status !== WorkflowStatus.ACTIVE}
                  className="w-full px-3 py-2 text-sm font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded-md hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Approve Step
                </button>
                <button
                  onClick={() => addToLog('Step rejected by user')}
                  disabled={isExecuting || workflowInstance.status !== WorkflowStatus.ACTIVE}
                  className="w-full px-3 py-2 text-sm font-medium text-red-700 bg-red-100 border border-red-300 rounded-md hover:bg-red-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Reject Step
                </button>
              </div>
            </div>

            {/* Execution Log */}
            <div className="bg-white border rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Execution Log</h3>
              <div className="h-48 overflow-y-auto bg-gray-50 rounded p-3 text-xs font-mono">
                {executionLog.length === 0 ? (
                  <div className="text-gray-500">No execution logs yet...</div>
                ) : (
                  executionLog.map((log, index) => (
                    <div key={index} className="mb-1 text-gray-700">
                      {log}
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Participants */}
            <div className="bg-white border rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Participants</h3>
              <div className="space-y-2">
                {workflowInstance.participants.map((participant, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="text-gray-700">{participant.userId}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      participant.role === 'initiator' ? 'bg-blue-100 text-blue-800' :
                      participant.role === 'approver' ? 'bg-green-100 text-green-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {participant.role}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default WorkflowExecutor
