import { memo, useState } from 'react'
import { Handle, Position, NodeProps } from '@xyflow/react'

const NotificationNode = memo(({ data, selected }: NodeProps) => {
  const nodeData = data as any
  const [showTooltip, setShowTooltip] = useState(false)

  return (
    <div
      className={`px-4 py-3 shadow-md rounded-lg bg-blue-50 border-2 ${
        selected ? 'border-blue-500' : 'border-blue-300'
      } min-w-[160px] relative`}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 !bg-blue-500 !border-2 !border-white"
      />

      <div className="flex items-center space-x-2">
        <div className="text-blue-600">
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2zM4 7h8V5H4v2z" />
          </svg>
        </div>
        <div className="flex-1">
          <div className="text-sm font-medium text-gray-900 truncate">
            {nodeData.label}
          </div>
          {nodeData.description && (
            <div className="text-xs text-gray-500 truncate">
              {nodeData.description}
            </div>
          )}
        </div>
      </div>

      {/* Notification type indicator */}
      <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-400 rounded-full border-2 border-white" />

      {/* Tooltip */}
      {showTooltip && nodeData.description && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap z-10">
          {nodeData.description}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900" />
        </div>
      )}

      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 !bg-blue-500 !border-2 !border-white"
      />
    </div>
  )
})

NotificationNode.displayName = 'NotificationNode'

export default NotificationNode
