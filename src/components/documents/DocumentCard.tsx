import { useState } from 'react'
import { Document, DocumentStatus } from '../../types'
import { useDocumentActions } from '../../hooks/useDocuments'
import { useNotifications } from '../../hooks/useUI'
import { formatFileSize, formatRelativeTime, getDocumentStatusColor, getDocumentTypeIcon } from '../../utils/dataUtils'
import DocumentViewer from './DocumentViewer'
import DocumentMetadataEditor from './DocumentMetadataEditor'

interface DocumentCardProps {
  document: Document
  viewMode: 'grid' | 'list'
}

const DocumentCard = ({ document, viewMode }: DocumentCardProps) => {
  const { handleStatusUpdate, handleDelete } = useDocumentActions()
  const { showSuccess, showError } = useNotifications()
  const [showActions, setShowActions] = useState(false)
  const [showViewer, setShowViewer] = useState(false)
  const [showEditor, setShowEditor] = useState(false)
  const [loading, setLoading] = useState(false)

  const handleStatusChange = async (status: DocumentStatus) => {
    setLoading(true)
    const result = await handleStatusUpdate(document.id, status)
    setLoading(false)

    if (result.success) {
      showSuccess('Status Updated', `Document status changed to ${status}`)
    } else {
      showError('Update Failed', result.error || 'Failed to update status')
    }
    setShowActions(false)
  }

  const handleDeleteDocument = async () => {
    if (window.confirm('Are you sure you want to delete this document?')) {
      setLoading(true)
      const result = await handleDelete(document.id)
      setLoading(false)

      if (result.success) {
        showSuccess('Document Deleted', 'Document has been successfully deleted')
      } else {
        showError('Delete Failed', result.error || 'Failed to delete document')
      }
    }
    setShowActions(false)
  }

  if (viewMode === 'list') {
    return (
      <div className="bg-white shadow rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 flex-1 min-w-0">
            <div className="text-2xl">{getDocumentTypeIcon(document.type)}</div>
            <div className="flex-1 min-w-0">
              <button
                onClick={() => setShowViewer(true)}
                className="text-left w-full"
              >
                <h3 className="text-sm font-medium text-gray-900 truncate hover:text-blue-600">
                  {document.name}
                </h3>
                <p className="text-sm text-gray-500 truncate">
                  {document.metadata.description || 'No description'}
                </p>
              </button>
            </div>
            <div className="hidden sm:flex items-center space-x-4 text-sm text-gray-500">
              <span>{formatFileSize(document.size)}</span>
              <span>{formatRelativeTime(document.updatedAt)}</span>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getDocumentStatusColor(document.status)}`}>
                {document.status.replace('_', ' ')}
              </span>
            </div>
          </div>
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
              disabled={loading}
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
              </svg>
            </button>
            {showActions && <ActionMenu onStatusChange={handleStatusChange} onDelete={handleDeleteDocument} onEdit={() => setShowEditor(true)} />}
          </div>
        </div>

        {/* Document Viewer Modal */}
        <DocumentViewer
          document={document}
          isOpen={showViewer}
          onClose={() => setShowViewer(false)}
        />

        {/* Document Metadata Editor Modal */}
        <DocumentMetadataEditor
          document={document}
          isOpen={showEditor}
          onClose={() => setShowEditor(false)}
        />
      </div>
    )
  }

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow duration-200">
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="text-2xl">{getDocumentTypeIcon(document.type)}</div>
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
              disabled={loading}
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
              </svg>
            </button>
            {showActions && <ActionMenu onStatusChange={handleStatusChange} onDelete={handleDeleteDocument} onEdit={() => setShowEditor(true)} />}
          </div>
        </div>

        <button
          onClick={() => setShowViewer(true)}
          className="text-left w-full"
        >
          <h3 className="text-sm font-medium text-gray-900 truncate hover:text-blue-600 mb-1">
            {document.name}
          </h3>
          <p className="text-xs text-gray-500 line-clamp-2 mb-3">
            {document.metadata.description || 'No description available'}
          </p>
        </button>

        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>{formatFileSize(document.size)}</span>
          <span>{formatRelativeTime(document.updatedAt)}</span>
        </div>

        <div className="mt-3">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getDocumentStatusColor(document.status)}`}>
            {document.status.replace('_', ' ')}
          </span>
        </div>

        {document.tags.length > 0 && (
          <div className="mt-3 flex flex-wrap gap-1">
            {document.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
              >
                {tag}
              </span>
            ))}
            {document.tags.length > 3 && (
              <span className="text-xs text-gray-500">+{document.tags.length - 3} more</span>
            )}
          </div>
        )}
      </div>

      {/* Document Viewer Modal */}
      <DocumentViewer
        document={document}
        isOpen={showViewer}
        onClose={() => setShowViewer(false)}
      />

      {/* Document Metadata Editor Modal */}
      <DocumentMetadataEditor
        document={document}
        isOpen={showEditor}
        onClose={() => setShowEditor(false)}
      />
    </div>
  )
}

const ActionMenu = ({ onStatusChange, onDelete, onEdit }: {
  onStatusChange: (status: DocumentStatus) => void
  onDelete: () => void
  onEdit: () => void
}) => (
  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
    <div className="py-1">
      <button
        onClick={onEdit}
        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
      >
        Edit Metadata
      </button>
      <hr className="my-1" />
      <button
        onClick={() => onStatusChange(DocumentStatus.APPROVED)}
        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
      >
        Approve
      </button>
      <button
        onClick={() => onStatusChange(DocumentStatus.UNDER_REVIEW)}
        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
      >
        Mark for Review
      </button>
      <button
        onClick={() => onStatusChange(DocumentStatus.ARCHIVED)}
        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
      >
        Archive
      </button>
      <hr className="my-1" />
      <button
        onClick={onDelete}
        className="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
      >
        Delete
      </button>
    </div>
  </div>
)

export default DocumentCard
