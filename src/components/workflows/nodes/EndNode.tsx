import { memo } from 'react'
import { Handle, Position, NodeProps } from '@xyflow/react'

const EndNode = memo(({ data, selected }: NodeProps) => {
  const nodeData = data as any
  return (
    <div className={`px-4 py-2 shadow-md rounded-full bg-red-500 border-2 ${
      selected ? 'border-red-700' : 'border-red-600'
    } min-w-[120px]`}>
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 !bg-red-600 !border-2 !border-white"
      />

      <div className="flex items-center justify-center">
        <div className="text-white font-medium text-sm text-center">
          <div className="flex items-center justify-center mb-1">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zM12 7a1 1 0 012 0v4a1 1 0 11-2 0V7z" clipRule="evenodd" />
            </svg>
            End
          </div>
          {nodeData.label && nodeData.label !== 'End' && (
            <div className="text-xs opacity-90">{nodeData.label}</div>
          )}
        </div>
      </div>
    </div>
  )
})

EndNode.displayName = 'EndNode'

export default EndNode
