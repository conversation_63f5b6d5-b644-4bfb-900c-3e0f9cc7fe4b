import { useState, useMemo } from 'react'
import { useWorkflows } from '../../hooks/useWorkflows'
import { WorkflowStatus } from '../../types'
import { formatDate, formatRelativeTime } from '../../utils/dataUtils'

const WorkflowAnalytics = () => {
  const { instances, templates } = useWorkflows()
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d')
  // const [selectedMetric, setSelectedMetric] = useState<'completion' | 'performance' | 'bottlenecks'>('completion')

  // Calculate date range
  const getDateRange = () => {
    const now = new Date()
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365
    const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000)
    return { startDate, endDate: now }
  }

  const { startDate, endDate } = getDateRange()

  // Filter instances by date range
  const filteredInstances = instances.filter(instance =>
    instance.createdAt >= startDate && instance.createdAt <= endDate
  )

  // Calculate analytics
  const analytics = useMemo(() => {
    const total = filteredInstances.length
    const completed = filteredInstances.filter(i => i.status === WorkflowStatus.COMPLETED).length
    const active = filteredInstances.filter(i => i.status === WorkflowStatus.ACTIVE).length
    const failed = filteredInstances.filter(i => i.status === WorkflowStatus.FAILED).length
    const cancelled = filteredInstances.filter(i => i.status === WorkflowStatus.CANCELLED).length

    const completionRate = total > 0 ? (completed / total) * 100 : 0
    const failureRate = total > 0 ? (failed / total) * 100 : 0

    // Average completion time (for completed workflows)
    const completedWorkflows = filteredInstances.filter(i =>
      i.status === WorkflowStatus.COMPLETED && i.completedAt
    )
    const avgCompletionTime = completedWorkflows.length > 0
      ? completedWorkflows.reduce((sum, w) => {
          const duration = w.completedAt!.getTime() - w.createdAt.getTime()
          return sum + duration
        }, 0) / completedWorkflows.length
      : 0

    // Template usage
    const templateUsage = templates.map(template => ({
      template,
      usage: filteredInstances.filter(i => i.templateId === template.id).length
    })).sort((a, b) => b.usage - a.usage)

    // Daily workflow creation
    const dailyCreation = []
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dayStart = new Date(d)
      const dayEnd = new Date(d)
      dayEnd.setHours(23, 59, 59, 999)

      const count = filteredInstances.filter(i =>
        i.createdAt >= dayStart && i.createdAt <= dayEnd
      ).length

      dailyCreation.push({
        date: new Date(d),
        count
      })
    }

    return {
      total,
      completed,
      active,
      failed,
      cancelled,
      completionRate,
      failureRate,
      avgCompletionTime,
      templateUsage,
      dailyCreation
    }
  }, [filteredInstances, templates, startDate, endDate])

  const formatDuration = (milliseconds: number) => {
    const hours = Math.floor(milliseconds / (1000 * 60 * 60))
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 24) {
      const days = Math.floor(hours / 24)
      return `${days}d ${hours % 24}h`
    }
    return `${hours}h ${minutes}m`
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Workflow Analytics</h2>
          <p className="mt-1 text-sm text-gray-600">
            Monitor workflow performance and identify optimization opportunities
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Workflows</dt>
                  <dd className="text-lg font-medium text-gray-900">{analytics.total}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Completion Rate</dt>
                  <dd className="text-lg font-medium text-gray-900">{analytics.completionRate.toFixed(1)}%</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Avg. Completion Time</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {analytics.avgCompletionTime > 0 ? formatDuration(analytics.avgCompletionTime) : 'N/A'}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Failure Rate</dt>
                  <dd className="text-lg font-medium text-gray-900">{analytics.failureRate.toFixed(1)}%</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Status Distribution */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Workflow Status Distribution</h3>
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-5">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{analytics.completed}</div>
              <div className="text-sm text-gray-500">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{analytics.active}</div>
              <div className="text-sm text-gray-500">Active</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{analytics.failed}</div>
              <div className="text-sm text-gray-500">Failed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">{analytics.cancelled}</div>
              <div className="text-sm text-gray-500">Cancelled</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{analytics.total}</div>
              <div className="text-sm text-gray-500">Total</div>
            </div>
          </div>
        </div>
      </div>

      {/* Template Usage */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Template Usage</h3>
          {analytics.templateUsage.length === 0 ? (
            <p className="text-gray-500">No template usage data available</p>
          ) : (
            <div className="space-y-3">
              {analytics.templateUsage.slice(0, 5).map(({ template, usage }) => (
                <div key={template.id} className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">{template.name}</p>
                    <p className="text-sm text-gray-500">{template.category}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="text-sm text-gray-900">{usage} uses</div>
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{
                          width: `${analytics.total > 0 ? (usage / analytics.total) * 100 : 0}%`
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Daily Creation Chart */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Daily Workflow Creation</h3>
          <div className="mt-4">
            <div className="flex items-end space-x-1 h-32">
              {analytics.dailyCreation.map((day, index) => {
                const maxCount = Math.max(...analytics.dailyCreation.map(d => d.count))
                const height = maxCount > 0 ? (day.count / maxCount) * 100 : 0

                return (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div
                      className="w-full bg-blue-500 rounded-t"
                      style={{ height: `${height}%` }}
                      title={`${formatDate(day.date)}: ${day.count} workflows`}
                    />
                    <div className="text-xs text-gray-500 mt-1 transform -rotate-45 origin-top-left">
                      {day.date.getDate()}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
          <div className="flow-root">
            <ul className="-mb-8">
              {filteredInstances
                .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
                .slice(0, 10)
                .map((instance, index) => (
                <li key={instance.id}>
                  <div className="relative pb-8">
                    {index !== 9 && (
                      <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" />
                    )}
                    <div className="relative flex space-x-3">
                      <div>
                        <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${
                          instance.status === WorkflowStatus.COMPLETED ? 'bg-green-500' :
                          instance.status === WorkflowStatus.ACTIVE ? 'bg-blue-500' :
                          instance.status === WorkflowStatus.FAILED ? 'bg-red-500' :
                          'bg-gray-500'
                        }`}>
                          <svg className="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </span>
                      </div>
                      <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                        <div>
                          <p className="text-sm text-gray-500">
                            <span className="font-medium text-gray-900">{instance.name}</span> {instance.status.toLowerCase()}
                          </p>
                          <p className="text-xs text-gray-500">Progress: {instance.progress}%</p>
                        </div>
                        <div className="text-right text-sm whitespace-nowrap text-gray-500">
                          {formatRelativeTime(instance.updatedAt)}
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default WorkflowAnalytics
