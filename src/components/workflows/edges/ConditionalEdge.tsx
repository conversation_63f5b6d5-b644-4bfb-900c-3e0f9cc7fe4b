import { memo } from 'react'
import { EdgeProps, getBezierPath, EdgeLabelRenderer } from '@xyflow/react'

const ConditionalEdge = memo(({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  selected,
  sourceHandleId,
}: EdgeProps) => {
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  })

  // Determine edge color based on condition
  const getEdgeColor = () => {
    if (selected) return '#3b82f6'

    if (sourceHandleId === 'true' || sourceHandleId === 'approved') {
      return '#10b981' // green
    } else if (sourceHandleId === 'false' || sourceHandleId === 'rejected') {
      return '#ef4444' // red
    }
    return '#6b7280' // gray
  }

  const getLabel = () => {
    if (data?.label) return data.label

    if (sourceHandleId === 'true') return 'Yes'
    if (sourceHandleId === 'false') return 'No'
    if (sourceHandleId === 'approved') return 'Approved'
    if (sourceHandleId === 'rejected') return 'Rejected'

    return ''
  }

  return (
    <>
      <path
        id={id}
        style={{
          ...style,
          stroke: getEdgeColor(),
          strokeWidth: selected ? 3 : 2,
          strokeDasharray: sourceHandleId === 'false' || sourceHandleId === 'rejected' ? '5,5' : 'none',
        }}
        className="react-flow__edge-path"
        d={edgePath}
        markerEnd="url(#react-flow__arrowclosed)"
      />
      {getLabel() && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              fontSize: 11,
              pointerEvents: 'all',
            }}
            className={`nodrag nopan px-2 py-1 rounded shadow-sm border text-white font-medium ${
              sourceHandleId === 'true' || sourceHandleId === 'approved'
                ? 'bg-green-500 border-green-600'
                : sourceHandleId === 'false' || sourceHandleId === 'rejected'
                ? 'bg-red-500 border-red-600'
                : 'bg-gray-500 border-gray-600'
            }`}
          >
            {String(getLabel())}
          </div>
        </EdgeLabelRenderer>
      )}
    </>
  )
})

ConditionalEdge.displayName = 'ConditionalEdge'

export default ConditionalEdge
