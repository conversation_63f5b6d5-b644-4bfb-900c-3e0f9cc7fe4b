import { NodeType } from '../../types'

interface NodePaletteProps {
  isVisible: boolean
}

const NodePalette = ({ isVisible }: NodePaletteProps) => {
  const onDragStart = (event: React.DragEvent, nodeType: NodeType) => {
    event.dataTransfer.setData('application/reactflow', nodeType)
    event.dataTransfer.effectAllowed = 'move'
  }

  const nodeTypes = [
    {
      type: NodeType.START,
      label: 'Start',
      description: 'Workflow starting point',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
        </svg>
      ),
      color: 'bg-green-500 text-white',
      borderColor: 'border-green-600',
    },
    {
      type: NodeType.END,
      label: 'End',
      description: 'Workflow ending point',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zM12 7a1 1 0 012 0v4a1 1 0 11-2 0V7z" clipRule="evenodd" />
        </svg>
      ),
      color: 'bg-red-500 text-white',
      borderColor: 'border-red-600',
    },
    {
      type: NodeType.DOCUMENT_UPLOAD,
      label: 'Document Upload',
      description: 'Upload documents to the workflow',
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
      ),
      color: 'bg-blue-500 text-white',
      borderColor: 'border-blue-600',
    },
    {
      type: NodeType.DOCUMENT_REVIEW,
      label: 'Document Review',
      description: 'Review and validate documents',
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: 'bg-indigo-500 text-white',
      borderColor: 'border-indigo-600',
    },
    {
      type: NodeType.APPROVAL,
      label: 'Approval',
      description: 'Approval or rejection step',
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: 'bg-amber-500 text-white',
      borderColor: 'border-amber-600',
    },
    {
      type: NodeType.CONDITIONAL,
      label: 'Conditional',
      description: 'Branch workflow based on conditions',
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: 'bg-purple-500 text-white',
      borderColor: 'border-purple-600',
    },
    {
      type: NodeType.NOTIFICATION,
      label: 'Notification',
      description: 'Send notifications to users',
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2zM4 7h8V5H4v2z" />
        </svg>
      ),
      color: 'bg-cyan-500 text-white',
      borderColor: 'border-cyan-600',
    },
  ]

  if (!isVisible) return null

  return (
    <div className="w-64 bg-white border-r border-gray-200 p-4 overflow-y-auto">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Workflow Nodes</h3>
        <p className="text-sm text-gray-600">Drag nodes to the canvas to build your workflow</p>
      </div>

      <div className="space-y-3">
        {nodeTypes.map((nodeType) => (
          <div
            key={nodeType.type}
            className={`p-3 rounded-lg border-2 cursor-move hover:shadow-md transition-shadow ${nodeType.color} ${nodeType.borderColor}`}
            draggable
            onDragStart={(event) => onDragStart(event, nodeType.type)}
          >
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                {nodeType.icon}
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium">
                  {nodeType.label}
                </div>
                <div className="text-xs opacity-90">
                  {nodeType.description}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 p-3 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Tips</h4>
        <ul className="text-xs text-gray-600 space-y-1">
          <li>• Start with a Start node</li>
          <li>• End with an End node</li>
          <li>• Connect nodes by dragging from handles</li>
          <li>• Use Conditional nodes for branching</li>
          <li>• Approval nodes have two outputs</li>
        </ul>
      </div>
    </div>
  )
}

export default NodePalette
