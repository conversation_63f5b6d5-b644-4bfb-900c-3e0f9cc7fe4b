interface WorkflowToolbarProps {
  workflowName: string
  workflowDescription: string
  onNameChange: (name: string) => void
  onDescriptionChange: (description: string) => void
  mode: 'view' | 'edit' | 'create'
  onModeChange: (mode: 'view' | 'edit' | 'create') => void
  onSave: () => void
  onClear: () => void
  showPalette: boolean
  onTogglePalette: () => void
  readOnly: boolean
  hasNodes: boolean
}

const WorkflowToolbar = ({
  workflowName,
  workflowDescription,
  onNameChange,
  onDescriptionChange,
  mode,
  onModeChange,
  onSave,
  onClear,
  showPalette,
  onTogglePalette,
  readOnly,
  hasNodes
}: WorkflowToolbarProps) => {
  return (
    <div className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        {/* Left side - Workflow info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-4">
            <div className="flex-1 min-w-0">
              {mode === 'view' ? (
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 truncate">
                    {workflowName}
                  </h2>
                  {workflowDescription && (
                    <p className="text-sm text-gray-600 truncate">
                      {workflowDescription}
                    </p>
                  )}
                </div>
              ) : (
                <div className="space-y-2">
                  <input
                    type="text"
                    value={workflowName}
                    onChange={(e) => onNameChange(e.target.value)}
                    className="text-lg font-semibold text-gray-900 bg-transparent border-none p-0 focus:ring-0 focus:outline-none"
                    placeholder="Workflow name..."
                  />
                  <input
                    type="text"
                    value={workflowDescription}
                    onChange={(e) => onDescriptionChange(e.target.value)}
                    className="text-sm text-gray-600 bg-transparent border-none p-0 focus:ring-0 focus:outline-none w-full"
                    placeholder="Add description..."
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center space-x-3">
          {/* Mode Toggle */}
          {!readOnly && (
            <div className="flex rounded-md shadow-sm">
              <button
                onClick={() => onModeChange('view')}
                className={`px-3 py-2 text-sm font-medium rounded-l-md border ${
                  mode === 'view'
                    ? 'bg-gray-100 border-gray-300 text-gray-900'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </button>
              <button
                onClick={() => onModeChange('edit')}
                className={`px-3 py-2 text-sm font-medium border-t border-b ${
                  mode === 'edit'
                    ? 'bg-yellow-100 border-yellow-300 text-yellow-900'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
              <button
                onClick={() => onModeChange('create')}
                className={`px-3 py-2 text-sm font-medium rounded-r-md border ${
                  mode === 'create'
                    ? 'bg-blue-100 border-blue-300 text-blue-900'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </button>
            </div>
          )}

          {/* Palette Toggle */}
          {mode !== 'view' && (
            <button
              onClick={onTogglePalette}
              className={`p-2 rounded-md border ${
                showPalette
                  ? 'bg-blue-50 border-blue-300 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
              title="Toggle node palette"
            >
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </button>
          )}

          {/* Action Buttons */}
          {mode !== 'view' && (
            <>
              <button
                onClick={onClear}
                disabled={!hasNodes}
                className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Clear
              </button>
              <button
                onClick={onSave}
                disabled={!hasNodes}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Save Workflow
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default WorkflowToolbar
