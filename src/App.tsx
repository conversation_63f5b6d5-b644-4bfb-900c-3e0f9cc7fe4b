import { ReactFlow, Background, Controls } from '@xyflow/react'
import '@xyflow/react/dist/style.css'
import './App.css'

const initialNodes = [
  {
    id: '1',
    position: { x: 100, y: 100 },
    data: { label: 'Start Document Workflow' },
    type: 'input',
  },
  {
    id: '2',
    position: { x: 300, y: 200 },
    data: { label: 'Document Processing' },
  },
  {
    id: '3',
    position: { x: 500, y: 100 },
    data: { label: 'Workflow Complete' },
    type: 'output',
  },
]

const initialEdges = [
  { id: 'e1-2', source: '1', target: '2' },
  { id: 'e2-3', source: '2', target: '3' },
]

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Document Management Workflow</h1>
        <p>
          Built with React and @xyflow/react
        </p>
        <p className="status">
          ✅ Phase 1: Project Setup Complete
        </p>
      </header>
      <div style={{ height: '400px', border: '1px solid #ddd', margin: '20px 0' }}>
        <ReactFlow
          nodes={initialNodes}
          edges={initialEdges}
          fitView
        >
          <Background />
          <Controls />
        </ReactFlow>
      </div>
    </div>
  )
}

export default App
