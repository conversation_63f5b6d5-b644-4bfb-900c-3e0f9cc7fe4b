import { memo, useState } from 'react'
import { Handle, Position, NodeProps } from '@xyflow/react'

const ConditionalNode = memo(({ data, selected }: NodeProps) => {
  const nodeData = data as any
  const [showTooltip, setShowTooltip] = useState(false)

  return (
    <div
      className={`px-3 py-3 shadow-md bg-purple-50 border-2 ${
        selected ? 'border-purple-500' : 'border-purple-300'
      } min-w-[140px] relative`}
      style={{
        clipPath: 'polygon(20% 0%, 80% 0%, 100% 50%, 80% 100%, 20% 100%, 0% 50%)'
      }}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 !bg-purple-500 !border-2 !border-white"
      />

      <div className="flex items-center justify-center">
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <svg className="w-4 h-4 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="text-xs font-medium text-gray-900">
            {nodeData.label}
          </div>
        </div>
      </div>

      {/* Tooltip */}
      {showTooltip && nodeData.description && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap z-10">
          {nodeData.description}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900" />
        </div>
      )}

      {/* Multiple outputs for different conditions */}
      <Handle
        type="source"
        position={Position.Right}
        id="true"
        className="w-3 h-3 !bg-green-500 !border-2 !border-white"
        style={{ top: '30%' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="false"
        className="w-3 h-3 !bg-red-500 !border-2 !border-white"
        style={{ top: '70%' }}
      />
    </div>
  )
})

ConditionalNode.displayName = 'ConditionalNode'

export default ConditionalNode
